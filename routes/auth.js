const express = require("express");
const User = require("../models/User");
const router = express.Router();
const bcrypt = require("bcrypt");
const jwt = require("jsonwebtoken");
const auth = require("../middleware/authMiddleware.js");

router.post("/register", async (req, res) => {
  try {
    const { email, password } = req.body;
    const exit = await User.findOne({ email });
    if (exit) return res.status(400).json({ message: "อีเมลซ้ำ" });
    const hash = await bcrypt.hash(password, 10);
    const user = new User({ email, password: hash });
    await user.save();
    res.status(201).json({ message: "ลงทะเบียนสำเร็จ" });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

router.post("/Login", async (req, res) => {
  try {
    const { email, password } = req.body;
    const user = await User.findOne({ email });
    if (!user) return res.status(400).json({ message: "ไม่พบอีเมลของคุณ" });
    const matchPassword = bcrypt.compare(password, user.password);
    if (!matchPassword)
      return res.status(400).json({ message: "รหัสไม่ถูกต้อง" });

    const token = jwt.sign(
      {
        user: {
          id: user._id,
        }
      },
      process.env.JWT_SECRET,
      { expiresIn: "1d" }
    );

    res.status(200).json({ message: "เข้าสู่ระบบ", token: token });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});
router.put("/Edit", auth, async (req, res) => {
  try {
    const { password } = req.body;
    const user = await User.findById(req.user.id);
    if (!user) return res.status(400).json({ message: "ไม่พบอีเมลของคุณ" });
    const hash = await bcrypt.hash(password, 10);
    if (await bcrypt.compare(password, user.password))
      return res.status(400).json({ message: "รหัสผ่านเหมือนเดิม" });
    user.password = hash;
    await user.save();
    res.status(200).json({ message: "แก้ไขสำเร็จ" });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

router.delete("/Delete", async (req, res) => {
  try {
    const user = await User.findById(req.user.id);
    if (!user) return res.status(400).json({ message: "ไม่พบอีเมล" });
    await user.deleteOne();
    res.status(200).json({ message: "ลบสำเร็จ" });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
});

module.exports = router;
