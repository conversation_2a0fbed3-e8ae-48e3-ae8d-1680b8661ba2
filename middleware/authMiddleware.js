const jwt = require("jsonwebtoken");

const authMiddleware = async (req ,res ,next) =>{
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startWith("Bearer ")) {
        return res.status(401).json({message: "ไม่ได้รับอนุญาตเข้าระบบ"});
    } 
    
    const token = authHeader.split(" ")[1];

    try{
        const decoded = jwt.verify(token , process.env.JWT_SECRET);
        req.user = { id: decoded.user.id};
        next();
    }
    catch(err){
        res.status(401).json({message: err.message});
    }

};

module.exports = authMiddleware;